# 🎯 TÍNH NĂNG GỢI Ý VOUCHER/KHUYẾN MÃI TỰ ĐỘNG - IMPLEMENTATION

## **📋 TỔNG QUAN**

Đã hoàn thành implementation tính năng gợi ý tạo voucher/khuyến mãi tự động cho ThanhHuyStore với các tính năng:

- ✅ **Phân tích thông minh**: Detect sản phẩm cần khuyến mãi
- ✅ **Discord Integration**: Thông báo tự động qua Discord webhook
- ✅ **Admin Dashboard**: Giao diện quản lý gợi ý
- ✅ **Automated Scheduling**: Cron job chạy tự động
- ✅ **Priority System**: Phân loại độ ưu tiên

---

## **🏗️ KIẾN TRÚC HỆ THỐNG**

### **1. Database Changes**

```prisma
// Thêm vào Product model
priority Int @default(0) // 0: normal, 1-10: priority levels

// Thêm vào NotificationType enum
PROMOTION_SUGGESTION
VOUCHER_SUGGESTION
```

### **2. Core Components**

#### **A. PromotionSuggestionEngine** (`src/app/lib/promotionSuggestionEngine.ts`)

- **Singleton pattern** để quản lý analysis
- **4 thuật toán phân tích**:
  - High stock products (tồn kho > 50, không bán 30 ngày)
  - Low sales products (ít đơn hàng + giá cao)
  - Category performance (nhiều sản phẩm ế ẩm)
  - High view low sales (xem nhiều nhưng không mua)

#### **B. DiscordWebhookService** (`src/app/lib/discordWebhook.ts`)

- **Rich embeds** với màu sắc theo priority
- **Detailed formatting** cho từng suggestion
- **Error handling** và retry logic
- **Test connection** functionality

#### **C. API Endpoints**

- `GET /api/admin/promotion-suggestions` - Lấy gợi ý
- `POST /api/admin/promotion-suggestions` - Chạy analysis + Discord
- `GET /api/cron/promotion-analysis` - Automated cron job

#### **D. Admin Component** (`src/app/components/admin/PromotionSuggestions.tsx`)

- **Real-time analysis** với loading states
- **Discord integration** buttons
- **Priority visualization** với color coding
- **Action buttons** cho từng suggestion

---

## **🧠 THUẬT TOÁN PHÂN TÍCH**

### **1. High Stock Analysis**

```typescript
// Điều kiện phát hiện:
- inStock > 50
- Không có đơn hàng trong 30 ngày
- Không có promotion hiện tại
- Tính số ngày tồn kho

// Gợi ý:
- Discount 5-20% tùy theo stock level
- Priority HIGH nếu stock > 100
```

### **2. Low Sales Analysis**

```typescript
// Điều kiện phát hiện:
- ≤ 2 đơn hàng trong 30 ngày
- Giá cao hơn 20% so với TB danh mục
- Còn hàng trong kho

// Gợi ý:
- Voucher thử nghiệm 15%
- Priority MEDIUM
```

### **3. Category Performance**

```typescript
// Điều kiện phát hiện:
- ≥ 5 sản phẩm có stock > 20
- Toàn danh mục performance kém

// Gợi ý:
- Khuyến mãi toàn danh mục 5-10%
- Priority HIGH nếu > 10 sản phẩm
```

### **4. High View Low Sales**

```typescript
// Điều kiện phát hiện:
- > 50 lượt xem trong 30 ngày
- ≤ 3 đơn hàng
- Tỷ lệ conversion thấp

// Gợi ý:
- Voucher 10% để test conversion
- Priority MEDIUM
```

---

## **🔧 SETUP & DEPLOYMENT**

### **1. Database Migration**

```bash
# Chạy migration để thêm priority field
npx prisma db push

# Hoặc tạo migration file
npx prisma migrate dev --name add-product-priority
```

### **2. Environment Variables**

```env
# Discord webhook URL (đã có)
DISCORD_ORDER_WEBHOOK_URL=https://discord.com/api/webhooks/1384809092597547008/...

# Cron job security
CRON_SECRET=your-secret-token
ADMIN_SECRET=your-admin-token
```

### **3. Cron Job Setup**

```bash
# Vercel cron (vercel.json)
{
  "crons": [
    {
      "path": "/api/cron/promotion-analysis",
      "schedule": "0 9 * * *"
    }
  ]
}

# Hoặc external cron service
curl -H "Authorization: Bearer your-secret-token" \
     https://your-domain.com/api/cron/promotion-analysis
```

---

## **📱 USAGE GUIDE**

### **1. Admin Dashboard**

- Truy cập admin panel
- Tìm component "Gợi Ý Khuyến Mãi Tự Động"
- Click "Chạy Phân Tích" để manual analysis
- Click "Phân Tích + Discord" để gửi thông báo

### **2. Discord Notifications**

- Tự động gửi khi có gợi ý mới
- Rich embeds với đầy đủ thông tin
- Priority color coding
- Action suggestions

### **3. Automated Analysis**

- Chạy hàng ngày lúc 9:00 AM
- Chỉ gửi Discord cho suggestions mới
- Tránh spam bằng 24h cooldown
- Error notifications qua Discord

---

## **🎨 UI/UX FEATURES**

### **Priority Color System**

- 🔴 **HIGH**: Red - Cần xử lý ngay
- 🟡 **MEDIUM**: Yellow - Cần quan tâm
- 🟢 **LOW**: Green - Có thể đợi

### **Suggestion Types**

- 🎫 **PRODUCT_VOUCHER**: Voucher cho sản phẩm cụ thể
- 🏷️ **CATEGORY_PROMOTION**: Khuyến mãi toàn danh mục
- 📦 **STOCK_CLEARANCE**: Xả kho sản phẩm tồn

### **Action Buttons**

- ✅ **Thực hiện**: Redirect đến tạo voucher
- ❌ **Bỏ qua**: Dismiss suggestion
- 🔄 **Refresh**: Reload suggestions

---

## **📊 MONITORING & ANALYTICS**

### **Success Metrics**

- **Suggestion Accuracy**: % suggestions được thực hiện
- **Stock Reduction**: Giảm tồn kho sau khuyến mãi
- **Revenue Impact**: Doanh thu từ suggested promotions
- **Response Time**: Thời gian admin phản hồi

### **System Health**

- **Discord Delivery**: Success rate của notifications
- **Analysis Performance**: Thời gian chạy analysis
- **Database Load**: Impact lên database performance
- **Error Rates**: Tỷ lệ lỗi trong quá trình analysis

---

## **🔮 FUTURE ENHANCEMENTS**

### **Phase 2: Advanced Analytics**

- **Seasonal Adjustments**: Điều chỉnh logic theo mùa
- **Competitor Analysis**: So sánh giá với đối thủ
- **Customer Segmentation**: Gợi ý target audience
- **A/B Testing**: Test hiệu quả các mức discount

### **Phase 3: Automation**

- **Auto-create Vouchers**: Tự động tạo voucher với approval
- **Dynamic Pricing**: Điều chỉnh giá real-time
- **Inventory Forecasting**: Dự đoán tồn kho tương lai
- **ML Integration**: Machine learning cho accuracy

---

## **⚠️ IMPORTANT NOTES**

### **Security Considerations**

- Cron job được bảo vệ bằng secret token
- Admin endpoints require authentication
- Discord webhook URL được bảo mật
- Input validation cho tất cả APIs

### **Performance Considerations**

- Analysis chạy async để không block UI
- Database queries được optimize
- Caching cho repeated calculations
- Rate limiting cho Discord API

### **Business Rules**

- Chỉ gợi ý, không tự động tạo voucher
- Admin có quyền approve/reject
- Không duplicate suggestions trong 24h
- Respect existing promotions

---

## **✅ DEPLOYMENT CHECKLIST**

- [ ] Database migration completed
- [ ] Environment variables set
- [ ] Discord webhook tested
- [ ] Admin component integrated
- [ ] Cron job scheduled
- [ ] Error monitoring setup
- [ ] Documentation updated
- [ ] Team training completed

**🎉 Tính năng đã sẵn sàng để deploy và sử dụng!**
