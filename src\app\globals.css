@tailwind base;
@tailwind components;
@tailwind utilities;

.no-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari */
}
.no-scrollbar {
  -ms-overflow-style: none; /* IE & Edge */
  scrollbar-width: none; /* Firefox */
}

.flex-g-4 {
  flex: 4;
}

.overlay-post {
  background: linear-gradient(180deg, transparent 45%, #101426);
}

.swiper-button-next,
.swiper-button-prev {
  opacity: 0;
  transition: opacity 0.3s ease;
  background-color: #16b1ff;
  border-radius: 100%;
  width: 40px !important;
  height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.swiper:hover .swiper-button-next,
.swiper:hover .swiper-button-prev {
  opacity: 1; /* Hiển thị nút khi hover */
}

.swiper-button-next::after,
.swiper-button-prev::after {
  font-size: 18px !important;
  font-weight: bold !important;
  color: white;
}

/* Professional AdminSideBar scrollbar - UX/UI Expert Design */
.admin-sidebar-scrollbar {
  /* Prevent horizontal overflow */
  overflow-x: hidden !important;
  overflow-y: auto;
  /* Smooth scrolling */
  scroll-behavior: smooth;
}

.admin-sidebar-scrollbar::-webkit-scrollbar {
  width: 4px;
}

.admin-sidebar-scrollbar::-webkit-scrollbar-track {
  background: #f3f4f6; /* gray-100 */
  border-radius: 2px;
}

.admin-sidebar-scrollbar::-webkit-scrollbar-thumb {
  background: #d1d5db; /* gray-300 */
  border-radius: 2px;
  transition: background-color 0.2s ease;
}

.admin-sidebar-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #9ca3af; /* gray-400 */
}

/* Firefox scrollbar */
.admin-sidebar-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f3f4f6;
}

/* Prevent horizontal overflow globally for sidebar */
.admin-sidebar-container {
  overflow-x: hidden !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

.admin-sidebar-container * {
  box-sizing: border-box !important;
  max-width: 100% !important;
}

/* Ensure text doesn't cause overflow */
.admin-sidebar-text {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  max-width: 100% !important;
}

/* Custom scrollbar styles for Kanban */
.scrollbar-thin::-webkit-scrollbar {
  height: 12px;
  width: 12px;
}

.scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 6px;
  border: 2px solid transparent;
  background-clip: content-box;
}

.scrollbar-thumb-gray-300::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af;
}

.scrollbar-track-gray-100::-webkit-scrollbar-track {
  background-color: #f9fafb;
  border-radius: 6px;
}

.scrollbar-thin::-webkit-scrollbar-corner {
  background-color: #f9fafb;
}

/* NProgress Custom Styles for ThanhHuyStore */

/* Main progress bar */
#nprogress {
  pointer-events: none;
}

#nprogress .bar {
  background: linear-gradient(90deg, #3b82f6, #1d4ed8, #2563eb);
  position: fixed;
  z-index: 9999;
  top: 0;
  left: 0;
  width: 100%;
  height: 3.5px;
  border-radius: 0 0 2px 2px;
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
}

/* Fancy blur effect */
#nprogress .peg {
  display: block;
  position: absolute;
  right: 0px;
  width: 100px;
  height: 100%;
  box-shadow: 0 0 10px #3b82f6, 0 0 5px #3b82f6;
  opacity: 1;
  transform: rotate(3deg) translate(0px, -4px);
}

/* Remove spinner (we disabled it in config) */
#nprogress .spinner {
  display: none;
}

/* Smooth animation */
#nprogress .bar {
  transition: all 0.3s ease-out;
}

/* Custom pulse animation */
@keyframes nprogress-pulse {
  0% {
    opacity: 1;
    transform: scaleX(1);
  }
  50% {
    opacity: 0.8;
    transform: scaleX(1.02);
  }
  100% {
    opacity: 1;
    transform: scaleX(1);
  }
}

#nprogress .bar {
  animation: nprogress-pulse 2s ease-in-out infinite;
}

/* Mobile optimization */
@media (max-width: 768px) {
  #nprogress .bar {
    height: 2px;
  }
}
