'use client';

import Image from 'next/image';

interface AvatarProps {
	user?: any;
	small?: boolean;
}

const Avatar: React.FC<AvatarProps> = ({ user, small }) => {
	return (
		<div className="relative">
			<div
				className={`relative inline-block rounded-full overflow-hidden ${
					small ? 'h-8 w-8' : 'h-9 w-9 md:h-11 md:w-11'
				}`}
			>
				<Image alt="Avatar" src="/no-avatar-2.jpg" fill sizes="100%" />
			</div>
			<span
				className={`absolute block rounded-full bg-green-500 ring-2 ring-white top-0 right-0 ${
					small ? 'h-2 w-2' : 'h-2 w-2 md:h-3 md:w-3'
				}`}
			></span>
		</div>
	);
};

export default Avatar;
