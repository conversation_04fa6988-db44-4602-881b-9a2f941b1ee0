//Config prisma, defind model in db
datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model Account {
  id                String          @id @default(auto()) @map("_id") @db.ObjectId
  userId            String          @db.ObjectId
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?         @db.String
  access_token      String?         @db.String
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?         @db.String
  session_state     String?

  user              User            @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}


model User {
  id             String             @id @default(auto()) @map("_id") @db.ObjectId
  name           String?
  email          String             @unique
  emailVerified  Boolean?
  phoneNumber    String?
  image          String?
  hashedPassword String?
  createAt       DateTime           @default(now())
  updateAt       DateTime           @updatedAt
  lastLogin      DateTime?
  role           Role               @default(USER)

  resetPasswordToken   String?
  resetPasswordExpires DateTime?

  // <PERSON>h mục sản phẩm đã mua (để gửi email marketing)
  purchasedCategories String[] @default([])

  accounts        Account[]
  orders          Order[]
  reviews         Review[]
  articleReviews  ArticleReview[]
  notifications   Notification[]
  sentNotifications Notification[] @relation("NotificationFromUser")
  activities      Activity[]
  userVouchers    UserVoucher[]
  analyticsEvents AnalyticsEvent[]
  returnRequests  ReturnRequest[]

  chatRoomIds     String[]          @db.ObjectId
  chatRoom        ChatRoom[]        @relation(fields: [chatRoomIds], references: [id])

  seenMessageIds  String[]          @db.ObjectId
  seenMessage     Message[]         @relation("Seen", fields: [seenMessageIds], references: [id])
  Message         Message[]
}

model Order {
  id              String            @id @default(auto()) @map("_id") @db.ObjectId
  userId          String            @db.ObjectId
  amount          Float
  currency        String
  status          OrderStatus       @default(pending)
  deliveryStatus  DeliveryStatus?
  createDate      DateTime          @default(now())
  paymentIntentId String            @unique
  phoneNumber     String?
  address         Address?
  paymentMethod   String?
  shippingFee     Float?

  // Voucher information
  voucherId       String?           @db.ObjectId
  voucherCode     String?
  discountAmount  Float?            @default(0)
  originalAmount  Float?

  // Cancel information
  cancelReason    String?
  cancelDate      DateTime?

  // Shipping information
  shippingCode    String?           // Mã vận đơn GHN
  shippingStatus  String?           // Trạng thái vận chuyển
  shippingData    Json?             // Dữ liệu từ GHN

  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  voucher         Voucher?          @relation(fields: [voucherId], references: [id])
  products        CartProductType[]
  returnRequests  ReturnRequest[]
}

type CartProductType {
  id          String
  name        String
  description String
  category    String
  brand       String?
  selectedImg Image
  quantity    Int
  price       Float
  inStock     Int

  // Variant support
  variantId   String?  // ID of selected variant
  attributes  Json?    // Selected attributes: {"color": "silver", "storage": "512gb"}
}

type Image {
  color     String
  colorCode String
  images    String[]
}

type Address {
  city        String
  country     String
  line1       String
  line2       String?
  postal_code String
}

model Product {
  id               String      @id @default(auto()) @map("_id") @db.ObjectId
  name             String
  description      String
  brand            String      @default("Apple")

  // Product type and pricing
  productType      ProductType @default(SIMPLE)
  price            Float?      // For simple products
  basePrice        Float?      // Base price for variant products

  categoryId       String      @db.ObjectId
  category         Category    @relation(fields: [categoryId], references: [id], onDelete: NoAction)
  inStock          Int?        // For simple products
  priority         Int         @default(0) // 0: normal, 1-10: priority levels
  createDate       DateTime?   @default(now())
  createdAt        DateTime    @default(now())
  updatedAt        DateTime    @updatedAt

  // Soft delete fields
  isDeleted        Boolean     @default(false)
  deletedAt        DateTime?
  deletedBy        String?

  images           Image[]
  reviews          Review[]
  notifications    Notification[]
  productPromotions ProductPromotion[]

  // Variant system relationships
  productAttributes ProductAttribute[]
  variants         ProductVariant[]
}

model Category {
  id          String      @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  slug        String
  image       String?
  icon        String?
  description String?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  parentId    String?     @db.ObjectId
  parent      Category?   @relation("Subcategories", fields: [parentId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  subcategories Category[] @relation("Subcategories")
  products    Product[]
}

model Review {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  userId      String    @db.ObjectId
  productId   String    @db.ObjectId
  rating      Int
  comment     String
  reply       String?
  createdDate DateTime  @default(now())
  updatedAt   DateTime? @updatedAt

  product     Product   @relation(fields: [productId], references: [id], onDelete: Cascade)
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Article {
  id          String            @id @default(auto()) @map("_id") @db.ObjectId
  userId      String            @db.ObjectId
  title       String
  image       String
  content     String
  viewCount   Int               @default(0)
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt

  reviews     ArticleReview[]
  categoryId  String?           @db.ObjectId
  category    ArticleCategory?  @relation(fields: [categoryId], references: [id])
}

model ArticleReview {
  id          String           @id @default(auto()) @map("_id") @db.ObjectId
  userId      String           @db.ObjectId
  articleId   String           @db.ObjectId
  rating      Int?
  comment     String?
  parentId    String?          @db.ObjectId // ID của bình luận cha (null nếu là bình luận gốc)
  parent      ArticleReview?   @relation("Replies", fields: [parentId], references: [id], onDelete: NoAction, onUpdate: NoAction) // Quan hệ với bình luận cha
  replies     ArticleReview[]  @relation("Replies")
  createdDate DateTime         @default(now())
  updatedAt   DateTime?        @updatedAt

  article     Article          @relation(fields: [articleId], references: [id], onDelete: Cascade)
  user        User             @relation(fields: [userId], references: [id], onDelete: Cascade)
}


model ArticleCategory {
  id          String            @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  slug        String?           // Trường này có thể chứa giá trị null, không cần @default(null)
  description String?
  icon        String?
  isActive    Boolean           @default(true)
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
  Article     Article[]
}


model ChatRoom {
  id            String          @id @default(auto()) @map("_id") @db.ObjectId
  userIds       String[]        @db.ObjectId
  messageIds    String[]        @db.ObjectId
  createdAt     DateTime        @default(now())
  lastMessageAt DateTime        @default(now())
  name          String?

  messages Message[]
  users    User[]               @relation(fields: [userIds], references: [id])
}

model Message {
  id         String             @id @default(auto()) @map("_id") @db.ObjectId
  chatroomId String             @db.ObjectId
  senderId   String             @db.ObjectId
  body       String?
  image      String?
  createdAt  DateTime           @default(now())
  seenIds    String[]           @db.ObjectId

  chatroom ChatRoom             @relation(fields: [chatroomId], references: [id], onDelete: Cascade)
  sender   User                 @relation(fields: [senderId], references: [id], onDelete: Cascade)
  seen     User[]               @relation("Seen", fields: [seenIds], references: [id])
}

model Banner {
  id              String   @id @default(auto()) @map("_id") @db.ObjectId
  name            String
  description     String?
  image           String
  imageResponsive String
  startDate       DateTime
  endDate         DateTime
  status          String
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
}

model Notification {
  id              String           @id @default(auto()) @map("_id") @db.ObjectId
  userId          String?           @db.ObjectId
  productId       String?          @db.ObjectId
  orderId         String?          @db.ObjectId
  messageId       String?          @db.ObjectId
  fromUserId      String?          @db.ObjectId
  type            NotificationType
  title           String
  message         String
  data            Json?            // Flexible data storage
  isRead          Boolean          @default(false)
  createdAt       DateTime         @default(now())
  updatedAt       DateTime?        @updatedAt

  user            User?       @relation(fields: [userId], references: [id], onDelete: Cascade)
  product         Product?   @relation(fields: [productId], references: [id], onDelete: Cascade)
  fromUser        User?       @relation("NotificationFromUser", fields: [fromUserId], references: [id], onDelete: Cascade)
}

model Activity {
  id          String       @id @default(auto()) @map("_id") @db.ObjectId
  userId      String       @db.ObjectId
  type        ActivityType
  title       String
  description String?
  data        Json?        // Flexible data storage for activity-specific information
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt

  user        User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([createdAt])
}

enum NotificationType {
  ORDER_PLACED
  COMMENT_RECEIVED
  MESSAGE_RECEIVED
  LOW_STOCK
  SYSTEM_ALERT
  PROMOTION_SUGGESTION
  VOUCHER_SUGGESTION
}

enum ActivityType {
  ORDER_CREATED
  ORDER_UPDATED
  ORDER_CANCELLED
  PAYMENT_SUCCESS
  COMMENT_REVIEW
  PROFILE_UPDATED
  PASSWORD_CHANGED
  EMAIL_CHANGED
}

enum Role {
    USER
    ADMIN
}

enum OrderStatus {
  pending
  confirmed
  canceled
  completed
}

enum DeliveryStatus {
  not_shipped
  in_transit
  delivered
  returning
  returned
}

enum ReturnType {
  EXCHANGE  // Đổi hàng
  RETURN    // Trả hàng
  REFUND    // Hoàn tiền
}

enum ReturnStatus {
  PENDING     // Chờ xử lý
  APPROVED    // Đã duyệt
  REJECTED    // Từ chối
  COMPLETED   // Hoàn tất
}

enum DiscountType {
  PERCENTAGE
  FIXED
}

enum VoucherType {
  NEW_USER
  RETARGETING
  UPSELL
  LOYALTY
  EVENT
  GENERAL
}

model Voucher {
  id              String      @id @default(auto()) @map("_id") @db.ObjectId
  code            String      @unique
  description     String?
  image           String?
  discountType    DiscountType
  discountValue   Float
  minOrderValue   Float?
  quantity        Int
  usedCount       Int         @default(0)
  maxUsagePerUser Int         @default(1)
  startDate       DateTime
  endDate         DateTime
  isActive        Boolean     @default(true)
  voucherType     VoucherType @default(GENERAL)
  targetUserIds   String[]    @db.ObjectId
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  userVouchers    UserVoucher[]
  orders          Order[]
}

model UserVoucher {
  id                  String   @id @default(auto()) @map("_id") @db.ObjectId
  userId              String   @db.ObjectId
  voucherId           String   @db.ObjectId
  usedAt              DateTime?
  createdAt           DateTime @default(now())

  // Voucher reservation fields
  orderId             String?  @db.ObjectId  // Final order ID after payment success
  reservedForOrderId  String?  // Temporary reservation during checkout
  reservedAt          DateTime? // When reservation was made

  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  voucher   Voucher  @relation(fields: [voucherId], references: [id], onDelete: Cascade)

  @@unique([userId, voucherId])
}

model ReturnRequest {
  id              String       @id @default(auto()) @map("_id") @db.ObjectId
  orderId         String       @db.ObjectId
  userId          String       @db.ObjectId
  type            ReturnType
  reason          String
  description     String?
  images          String[]     // Ảnh bằng chứng
  status          ReturnStatus @default(PENDING)
  adminNote       String?
  processedBy     String?      @db.ObjectId

  // Shipping info for return
  returnShippingCode String?    // Mã vận đơn trả hàng
  returnShippingFee  Float?     // Phí ship trả hàng

  createdAt       DateTime     @default(now())
  processedAt     DateTime?

  order           Order        @relation(fields: [orderId], references: [id], onDelete: Cascade)
  user            User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([orderId])
  @@index([userId])
  @@index([status])
}

model Promotion {
  id              String       @id @default(auto()) @map("_id") @db.ObjectId
  title           String
  description     String?
  discountType    DiscountType
  discountValue   Float
  maxDiscount     Float?
  startDate       DateTime
  endDate         DateTime
  isActive        Boolean      @default(true)
  applyToAll      Boolean      @default(false)
  productIds      String[]     @db.ObjectId
  categoryIds     String[]     @db.ObjectId
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @updatedAt
  productPromotions ProductPromotion[]
}

model ProductPromotion {
  id              String       @id @default(auto()) @map("_id") @db.ObjectId
  productId       String       @db.ObjectId
  promotionId     String       @db.ObjectId
  promotionalPrice Float
  startDate       DateTime
  endDate         DateTime
  isActive        Boolean      @default(true)
  priority        Int          @default(0) // Higher number = higher priority
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @updatedAt

  product         Product      @relation(fields: [productId], references: [id], onDelete: Cascade)
  promotion       Promotion    @relation(fields: [promotionId], references: [id], onDelete: Cascade)

  @@unique([productId, promotionId])
  @@index([productId])
  @@index([promotionId])
  @@index([startDate, endDate])
}

model AnalyticsEvent {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  userId      String?   @db.ObjectId
  sessionId   String?   // Anonymous tracking
  eventType   EventType
  entityType  String?   // 'product', 'article', 'category'
  entityId    String?   @db.ObjectId
  metadata    Json?     // Flexible data storage
  userAgent   String?
  ipAddress   String?
  referrer    String?
  path        String
  timestamp   DateTime  @default(now())

  user        User?     @relation(fields: [userId], references: [id])

  @@index([eventType])
  @@index([timestamp])
  @@index([userId])
}

enum EventType {
  PAGE_VIEW
  PRODUCT_VIEW
  PRODUCT_CLICK
  SEARCH
  PURCHASE
  ARTICLE_VIEW
}

// Product Variant System Enums
enum ProductType {
  SIMPLE
  VARIANT
}

enum AttributeType {
  TEXT
  COLOR
  NUMBER
  SELECT
}

enum DisplayType {
  BUTTON
  DROPDOWN
  COLOR_SWATCH
  TEXT_INPUT
  RADIO
  CHECKBOX
}

model AdminSettings {
  id                    String   @id @default(auto()) @map("_id") @db.ObjectId

  // Notification settings
  discordNotifications  Boolean  @default(true)
  orderNotifications    Boolean  @default(true)
  emailNotifications    Boolean  @default(true)
  pushNotifications     Boolean  @default(false)

  // System settings
  analyticsTracking     Boolean  @default(true)
  sessionTimeout        Int      @default(30) // minutes

  // Automation settings
  lowStockAlerts        Boolean  @default(true)
  chatbotSupport        Boolean  @default(false)
  autoVoucherSuggestion Boolean  @default(true)

  // Report settings
  dailyReports          Boolean  @default(true)
  reportInterval        Int      @default(24) // hours

  // Payment settings - CRITICAL
  codPayment            Boolean  @default(true)
  momoPayment           Boolean  @default(false)
  stripePayment         Boolean  @default(false)

  // Audit fields
  createdBy             String
  updatedBy             String
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
}

model AdminAuditLog {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  action    String   // 'UPDATE_SETTINGS', 'LOGIN', 'CREATE_PRODUCT', etc.
  userId    String
  details   String   // JSON string with details
  createdAt DateTime @default(now())
}

model ReportLog {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  type      String   // SCHEDULED, MANUAL, TEST
  interval  Float    // Report interval in hours
  success   Boolean  // Whether the report was sent successfully
  sentAt    DateTime // When the report was sent
  error     String?  // Error message if failed
  createdAt DateTime @default(now())

  @@map("report_logs")
}

// Product Variant System Models (Simplified)

// Product-specific attributes (like WooCommerce product attributes)
model ProductAttribute {
  id           String      @id @default(auto()) @map("_id") @db.ObjectId
  productId    String      @db.ObjectId
  name         String      // "color", "storage", "ram" (product-specific)
  label        String      // "Màu sắc", "Dung lượng", "RAM"
  type         AttributeType
  displayType  DisplayType @default(BUTTON)
  isRequired   Boolean     @default(true)
  isVariation  Boolean     @default(true) // Used for variation or just info
  position     Int         @default(0)
  description  String?     // Optional description
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt

  // Relationships
  product Product          @relation(fields: [productId], references: [id], onDelete: Cascade)
  values  AttributeValue[]

  @@index([productId])
  @@index([name])
}

// Attribute values (product-specific only)
model AttributeValue {
  id              String           @id @default(auto()) @map("_id") @db.ObjectId
  attributeId     String           @db.ObjectId
  value           String           // "silver", "128gb", "16gb"
  label           String           // "Bạc", "128GB", "16GB"
  description     String?          // Detailed description
  colorCode       String?          // For COLOR type: "#C0C0C0"
  imageUrl        String?          // Optional image for value
  priceAdjustment Float            @default(0) // Price modifier: +/- amount
  position        Int              @default(0) // Sort order
  isActive        Boolean          @default(true)
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt

  // Relationships
  attribute ProductAttribute @relation(fields: [attributeId], references: [id], onDelete: Cascade)

  @@index([attributeId])
  @@index([value])
}

// Product variants
model ProductVariant {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  productId  String   @db.ObjectId
  sku        String   @unique
  attributes Json     // {"color": "silver", "storage": "512gb", "ram": "16gb"}
  price      Float
  stock      Int      @default(0)
  images     Image[]  // Array of image objects with color/colorCode/images
  isActive   Boolean  @default(true)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Relationships
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@index([productId])
  @@index([isActive])
}
