/** @type {import('tailwindcss').Config} */
module.exports = {
	content: [
		'./src/pages/**/*.{js,ts,jsx,tsx,mdx}',
		'./src/components/**/*.{js,ts,jsx,tsx,mdx}',
		'./src/app/**/*.{js,ts,jsx,tsx,mdx}',
	],
	theme: {
		extend: {
			screens: {
				'3xl': '1600px',
			},
			keyframes: {
				'pulse-blue': {
					'0%, 100%': { transform: 'scale(1)', boxShadow: '0 0 0 0 rgba(43, 97, 255, 0.7)' },
					'50%': { transform: 'scale(1.05)', boxShadow: '0 0 0 10px rgba(43, 97, 255, 0)' },
				},
			},
			animation: {
				'pulse-blue': 'pulse-blue 2s infinite',
			},
		},
	},
	plugins: [require('tailwind-scrollbar')],
	variants: {},
};
