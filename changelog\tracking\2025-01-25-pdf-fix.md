# PDF Generation Fix - 2025-01-25

## Vấn đề
- PDF generation API trả về status 200 nhưng browser hiển thị "Failed to load PDF document"
- Nguyên nhân: PDFGenerator chỉ tạo text content thay vì PDF binary thực sự

## Gi<PERSON>i pháp đã thực hiện

### ✅ 1. Cậ<PERSON> nhật PDFGenerator Service
**File:** `src/app/services/pdfGenerator.ts`

**Thay đổi chính:**
- Import PDFKit library: `import PDFDocument from 'pdfkit';`
- Thay thế text-based generation bằng PDFKit binary generation
- Tạo PDF thực sự với layout chuyên nghiệp

**Trước:**
```typescript
async generateOrderInvoice(orderData: OrderData): Promise<Buffer> {
  const invoiceContent = this.generateInvoiceContent(orderData);
  return Buffer.from(invoiceContent, 'utf-8'); // ❌ Text content
}
```

**Sau:**
```typescript
async generateOrderInvoice(orderData: OrderData): Promise<Buffer> {
  return new Promise((resolve, reject) => {
    const doc = new PDFDocument({ margin: 50 });
    const chunks: Buffer[] = [];
    
    doc.on('data', chunk => chunks.push(chunk));
    doc.on('end', () => resolve(Buffer.concat(chunks)));
    doc.on('error', reject);
    
    this.generatePDFContent(doc, orderData); // ✅ Real PDF generation
    doc.end();
  });
}
```

### ✅ 2. Tạo PDF Layout chuyên nghiệp
**Method mới:** `generatePDFContent(doc: PDFDocument, orderData: OrderData)`

**Features:**
- Header với tên công ty và tiêu đề hóa đơn
- Thông tin đơn hàng (ngày tạo, mã đơn hàng)
- Thông tin khách hàng (tên, email, SĐT, địa chỉ)
- Bảng sản phẩm với header và styling
- Tổng kết với các khoản phí
- Footer với thông tin liên hệ
- Hỗ trợ multiple pages nếu danh sách sản phẩm dài

### ✅ 3. Kiểm tra tích hợp hiện có
**Các component đã có sẵn PDF functionality:**

1. **OrdersTable.tsx** (dòng 52-59):
```typescript
<button
  onClick={() => window.open(`/api/orders/${order.id}/pdf`, '_blank')}
  className='flex items-center gap-1 px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors'
  title='Xem hóa đơn PDF'
>
  <MdPictureAsPdf size={14} />
  PDF
</button>
```

2. **ActivityTimeline.tsx** - PDF links trong activity feed
3. **API Routes** - `/api/orders/[id]/pdf` và `/api/pdf/[fileId]`

## Kết quả

### ✅ Đã hoàn thành
- [x] Sửa PDFGenerator để tạo PDF binary thực sự
- [x] Test API endpoint trả về status 200
- [x] PDF có thể mở được trong browser
- [x] Tích hợp với OrdersTable component
- [x] Lưu trữ PDF trong MongoDB GridFS

### 🧪 Cần test thêm
- [ ] Test với các order có nhiều sản phẩm (multiple pages)
- [ ] Test với order không có địa chỉ
- [ ] Test với order có voucher/discount
- [ ] Test PDF download functionality

## Dependencies sử dụng
- `pdfkit`: ^0.17.1 (đã có sẵn)
- `@types/pdfkit`: ^0.14.0 (đã có sẵn)

## API Endpoints hoạt động
- ✅ `GET /api/orders/{orderId}/pdf` - Tạo và trả về PDF
- ✅ `GET /api/pdf/{fileId}` - Lấy PDF đã lưu từ MongoDB
- ✅ MongoDB GridFS storage cho PDF files

## Cách sử dụng
1. Vào Admin Dashboard
2. Trong OrdersTable, click button "PDF" bên cạnh mỗi order
3. PDF sẽ mở trong tab mới
4. PDF được cache trong MongoDB, lần sau sẽ load nhanh hơn
