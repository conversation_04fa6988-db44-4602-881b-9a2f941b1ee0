import { getArticlesBySearchParams } from '@/app/actions/getArticlesBySearchParams';
import SearchResult from './SearchResult';
import { getProductsBySearchParams, IParams } from '@/app/actions/getProductsBySearchParams';

// Keep force-dynamic due to searchParams usage
export const dynamic = 'force-dynamic';

interface PageProps {
  searchParams: IParams;
}

export function generateMetadata({ searchParams }: PageProps) {
  const searchTerm = searchParams?.searchTerm || 'tất cả sản phẩm';
  return {
    title: `<PERSON>ết quả tìm kiếm cho: "${searchTerm}"`
  };
}

const page: React.FC<PageProps> = async ({ searchParams }) => {
  const products = await getProductsBySearchParams(searchParams);
  const articles = await getArticlesBySearchParams(searchParams);
  return <SearchResult products={products} articles={articles} />;
};

export default page;
